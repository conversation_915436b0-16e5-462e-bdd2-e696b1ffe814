========================================
PANDUAN INSTALASI PYTHON
Cryptocurrency Price Prediction System
========================================

🐍 LANGKAH-LANGKAH INSTALASI PYTHON

1. DOWNLOAD PYTHON
   - Buka browser (Chrome, Firefox, Edge)
   - Kunjungi: https://www.python.org/downloads/
   - Klik tombol "Download Python 3.12.x" (RECOMMENDED)
   - Jangan gunakan Python 3.13 (masih terlalu baru, bisa ada bug)

2. INSTALASI PYTHON
   - Jalankan file installer yang sudah didownload
   - ✅ PENTING: CENTANG "Add Python to PATH" 
   - ✅ PENTING: CENTANG "Install for all users" (jika ada)
   - Klik "Install Now"
   - <PERSON><PERSON><PERSON> proses instalasi selesai
   - Klik "Close" setelah selesai

3. VERIFIKASI INSTALASI
   - Buka Command Prompt (cmd) atau PowerShell
   - Ketik: python --version
   - Seharusnya muncul: Python 3.12.x
   - Ketik: pip --version
   - Seharusnya muncul versi pip

========================================
🔧 TROUBLESHOOTING INSTALASI

MASALAH: "Python was not found"
SOLUSI:
- Restart Command Prompt/PowerShell
- Restart komputer
- Install ulang Python dengan centang "Add to PATH"

MASALAH: "Access denied" saat install
SOLUSI:
- Klik kanan installer → "Run as administrator"
- Atau install tanpa "Install for all users"

MASALAH: Python terinstall tapi tidak di PATH
SOLUSI:
- Cari folder instalasi Python (biasanya C:\Python312\)
- Tambahkan manual ke PATH:
  1. Windows + R → ketik "sysdm.cpl"
  2. Tab "Advanced" → "Environment Variables"
  3. Edit "Path" → Add folder Python

========================================
📍 LOKASI INSTALASI PYTHON YANG UMUM

Setelah instalasi, Python biasanya ada di:
- C:\Users\<USER>\AppData\Local\Programs\Python\Python312\
- C:\Python312\
- C:\Program Files\Python312\

========================================
✅ SETELAH PYTHON TERINSTALL

1. Buka Command Prompt baru
2. Navigasi ke folder project: cd d:\xxamp\htdocs\ML
3. Jalankan: START_HERE.bat
4. Atau jalankan: run_crypto_predictor.bat

========================================
🎯 REKOMENDASI VERSI PYTHON

✅ RECOMMENDED:
- Python 3.11.x (paling stabil)
- Python 3.12.x (terbaru yang stabil)

⚠️ HINDARI:
- Python 3.13.x (terlalu baru, mungkin ada bug)
- Python 3.9.x atau lebih lama (sudah outdated)

========================================
📞 BANTUAN TAMBAHAN

Jika masih bermasalah:
1. Pastikan download dari python.org (bukan Microsoft Store)
2. Disable antivirus sementara saat install
3. Install sebagai administrator
4. Restart komputer setelah install
5. Coba install Python 3.11 jika 3.12 bermasalah

========================================
🚀 SETELAH PYTHON SIAP

Jalankan file berikut untuk memulai:
- START_HERE.bat (launcher utama)
- run_crypto_predictor.bat (script otomatis)
- test_system.py (untuk testing)

Happy Coding! 🐍✨
