"""
Main Entry Point
Menjalankan sistem prediksi harga cryptocurrency
"""

import warnings
warnings.filterwarnings('ignore')

import sys
import os
from datetime import datetime

from crypto_predictor import CryptoPricePredictor

def print_banner():
    """
    Print banner aplikasi
    """
    print("="*80)
    print("🚀 CRYPTOCURRENCY PRICE PREDICTION SYSTEM 🚀")
    print("="*80)
    print("📊 Supported Cryptocurrencies: BTC, ETH, SOL, ADA, XRP")
    print("🤖 Model: Custom LSTM (Optimized for CPU)")
    print("📈 Features: Price prediction, Trading signals, Performance evaluation")
    print("="*80)

def print_system_info():
    """
    Print informasi sistem
    """
    print("\n💻 System Information:")
    print(f"Python Version: {sys.version.split()[0]}")
    print(f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working Directory: {os.getcwd()}")

def get_user_config():
    """
    Mendapatkan konfigurasi dari user
    
    Returns:
        dict: Konfigurasi user
    """
    print("\n⚙️ Configuration Setup:")
    
    # Default configuration (optimized for Core i3 + 16GB RAM)
    default_config = {
        'period': '2y',      # 2 tahun data historis
        'window_size': 60,   # 60 hari input sequence
        'hidden_size': 32,   # Hidden layer size (dikurangi untuk optimasi)
        'epochs': 30,        # Epochs training (dikurangi untuk optimasi)
        'batch_size': 16,    # Batch size (dikurangi untuk optimasi)
        'learning_rate': 0.001
    }
    
    print("📋 Default Configuration (Optimized for Core i3 + 16GB RAM):")
    for key, value in default_config.items():
        print(f"  {key}: {value}")
    
    # Tanya user apakah ingin menggunakan default atau custom
    use_default = input("\n❓ Use default configuration? (y/n): ").lower().strip()
    
    if use_default == 'y' or use_default == '':
        return default_config
    
    # Custom configuration
    config = {}
    
    try:
        config['period'] = input(f"Data period (default: {default_config['period']}): ").strip() or default_config['period']
        config['window_size'] = int(input(f"Window size (default: {default_config['window_size']}): ") or default_config['window_size'])
        config['hidden_size'] = int(input(f"Hidden layer size (default: {default_config['hidden_size']}): ") or default_config['hidden_size'])
        config['epochs'] = int(input(f"Training epochs (default: {default_config['epochs']}): ") or default_config['epochs'])
        config['batch_size'] = int(input(f"Batch size (default: {default_config['batch_size']}): ") or default_config['batch_size'])
        config['learning_rate'] = float(input(f"Learning rate (default: {default_config['learning_rate']}): ") or default_config['learning_rate'])
    except ValueError:
        print("⚠️ Invalid input, using default configuration")
        return default_config
    
    return config

def main():
    """
    Fungsi utama aplikasi
    """
    try:
        # Print banner dan info sistem
        print_banner()
        print_system_info()
        
        # Dapatkan konfigurasi
        config = get_user_config()
        
        print(f"\n🔧 Using Configuration:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        # Konfirmasi untuk memulai
        start_analysis = input("\n🚀 Start cryptocurrency analysis? (y/n): ").lower().strip()
        
        if start_analysis != 'y' and start_analysis != '':
            print("👋 Analysis cancelled. Goodbye!")
            return
        
        # Inisialisasi predictor
        print("\n🔄 Initializing Crypto Price Predictor...")
        predictor = CryptoPricePredictor(
            window_size=config['window_size'],
            hidden_size=config['hidden_size'],
            learning_rate=config['learning_rate']
        )
        
        # Jalankan analisis lengkap
        print("\n🎯 Starting full analysis...")
        results = predictor.run_full_analysis(
            period=config['period'],
            epochs=config['epochs'],
            batch_size=config['batch_size']
        )
        
        if results:
            print("\n🎉 Analysis completed successfully!")
            
            # Tampilkan ringkasan hasil
            training_results = results.get('training_results', [])
            trading_signals = results.get('trading_signals', [])
            
            print(f"\n📊 Summary:")
            print(f"  - Models trained: {len(training_results)}")
            print(f"  - Trading signals generated: {len(trading_signals)}")
            print(f"  - Plots saved: {len(training_results)} files")
            
            # Tanya apakah ingin melihat prediksi real-time
            show_predictions = input("\n🔮 Show individual predictions for each crypto? (y/n): ").lower().strip()
            
            if show_predictions == 'y':
                print("\n🔮 Individual Predictions:")
                print("-" * 60)
                
                for symbol in predictor.supported_cryptos:
                    if symbol in predictor.models:
                        prediction = predictor.predict_next_day(symbol)
                        if prediction:
                            print(f"{symbol:<6} | Current: ${prediction['current_price']:.4f} | "
                                  f"Predicted: ${prediction['predicted_price']:.4f} | "
                                  f"Change: {prediction['price_change_pct']:+.2f}%")
        
        else:
            print("❌ Analysis failed. Please check the error messages above.")
    
    except KeyboardInterrupt:
        print("\n\n⚠️ Analysis interrupted by user. Goodbye!")
    
    except Exception as e:
        print(f"\n❌ An error occurred: {str(e)}")
        print("Please check your internet connection and try again.")
    
    finally:
        print("\n" + "="*80)
        print("📝 Notes:")
        print("  - Prediction plots are saved as PNG files in the current directory")
        print("  - This is for educational purposes only, not financial advice")
        print("  - Always do your own research before making investment decisions")
        print("="*80)
        print("👋 Thank you for using Crypto Price Prediction System!")

if __name__ == "__main__":
    main()
