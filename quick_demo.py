"""
Quick Demo
Demo cepat untuk testing sistem dengan konfigurasi minimal
"""

import warnings
warnings.filterwarnings('ignore')

from crypto_predictor import CryptoPricePredictor

def quick_demo():
    """
    Demo cepat dengan konfigurasi minimal untuk testing
    """
    print("🚀 QUICK DEMO - Cryptocurrency Price Prediction")
    print("="*60)
    print("⚡ Konfigurasi minimal untuk testing cepat:")
    print("  - Period: 6 bulan")
    print("  - Window: 30 hari")
    print("  - Hidden: 16 neurons")
    print("  - Epochs: 10")
    print("  - Batch: 8")
    print("  - Crypto: BTC saja")
    print("="*60)
    
    try:
        # Inisialisasi dengan konfigurasi minimal
        predictor = CryptoPricePredictor(
            window_size=30,      # Dikurangi dari 60
            hidden_size=16,      # Dikurangi dari 32
            learning_rate=0.01   # Diperbesar untuk konvergensi cepat
        )
        
        print("\n🔄 Mengambil data BTC (6 bulan)...")
        
        # Ambil data hanya untuk BTC
        btc_data = predictor.data_fetcher.fetch_crypto_data('BTC', period='6mo')
        
        if btc_data is None or btc_data.empty:
            print("❌ Gagal mengambil data BTC")
            return
        
        predictor.crypto_data = {'BTC': btc_data}
        predictor.supported_cryptos = ['BTC']
        
        print(f"✅ Data BTC berhasil diambil: {len(btc_data)} hari")
        
        # Training dengan konfigurasi minimal
        print("\n🚀 Memulai training BTC (konfigurasi minimal)...")
        
        result = predictor.train_model_for_crypto(
            'BTC', 
            epochs=10,      # Dikurangi dari 30
            batch_size=8    # Dikurangi dari 16
        )
        
        if result:
            print("\n✅ Training berhasil!")
            
            # Generate prediksi
            prediction = predictor.predict_next_day('BTC')
            if prediction:
                print(f"\n🔮 Prediksi BTC:")
                print(f"  Harga saat ini: ${prediction['current_price']:.2f}")
                print(f"  Prediksi besok: ${prediction['predicted_price']:.2f}")
                print(f"  Perubahan: {prediction['price_change_pct']:+.2f}%")
                
                # Generate sinyal
                signal = predictor.evaluator.generate_trading_signal(
                    prediction['current_price'],
                    prediction['predicted_price'],
                    'BTC'
                )
                
                print(f"\n📊 Sinyal Trading:")
                print(f"  {signal['color']} {signal['signal']}")
            
            # Buat plot
            predictor.evaluator.plot_predictions('BTC_test', save_plot=True)
            
            print("\n🎉 Demo selesai!")
            print("📁 Plot disimpan sebagai BTC_test_prediction_plot.png")
            
        else:
            print("❌ Training gagal")
    
    except Exception as e:
        print(f"❌ Error dalam demo: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_demo()
