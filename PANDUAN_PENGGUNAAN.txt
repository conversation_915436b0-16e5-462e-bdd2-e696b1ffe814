========================================
PANDUAN PENGGUNAAN
Cryptocurrency Price Prediction System
========================================

🚀 CARA MENJALANKAN SISTEM

1. PERSIAPAN AWAL
   - Pastikan Python 3.7+ sudah terinstall
   - Buka Command Prompt atau PowerShell
   - Navigasi ke folder project: cd d:\xxamp\htdocs\ML

2. INSTALASI DEPENDENCIES
   Pilih salah satu cara:
   
   A<PERSON> batch file (RECOMMENDED):
      - Double-click "install_and_run.bat"
      - Atau jalankan: install_and_run.bat
   
   B. Menggunakan Python 3.13 spesifik:
      - Double-click "run_with_python313.bat"
      - Atau jalankan: run_with_python313.bat
   
   C. Manual installation:
      - pip install -r requirements.txt
      - python main.py

3. TESTING SISTEM
   Sebelum menjalankan analisis lengkap, test dulu:
   - python test_system.py
   
   Atau untuk demo cepat:
   - python quick_demo.py

========================================
📊 PILIHAN MENJALANKAN PROGRAM

1. ANALISIS LENGKAP (RECOMMENDED)
   - python main.py
   - Akan menganalisis semua crypto: BTC, ETH, SOL, ADA, XRP
   - Waktu: ~15-30 menit (tergantung spek laptop)

2. DEMO CEPAT
   - python quick_demo.py
   - Hanya menganalisis BTC dengan konfigurasi minimal
   - Waktu: ~3-5 menit

3. TESTING KOMPONEN
   - python test_system.py
   - Test semua komponen sistem
   - Waktu: ~1-2 menit

========================================
⚙️ KONFIGURASI UNTUK LAPTOP SPEK RENDAH

Jika laptop Anda lambat, gunakan konfigurasi ini di main.py:

DEFAULT (SUDAH OPTIMAL):
- Period: 2y (2 tahun data)
- Window: 60 hari
- Hidden: 32 neurons
- Epochs: 30
- Batch: 16

UNTUK LAPTOP SANGAT LAMBAT:
- Period: 1y (1 tahun data)
- Window: 30 hari
- Hidden: 16 neurons
- Epochs: 20
- Batch: 8

UNTUK TESTING CEPAT:
- Period: 6mo (6 bulan data)
- Window: 20 hari
- Hidden: 8 neurons
- Epochs: 10
- Batch: 4

========================================
📈 OUTPUT YANG DIHASILKAN

1. CONSOLE OUTPUT:
   - Progress training untuk setiap crypto
   - Metrics evaluasi (RMSE, MAE, MAPE)
   - Trading signals (BUY/SELL/HOLD)
   - Laporan ringkasan

2. FILE OUTPUT:
   - [CRYPTO]_test_prediction_plot.png (grafik prediksi)
   - Contoh: BTC_test_prediction_plot.png

3. TRADING SIGNALS:
   🟢 STRONG BUY: Prediksi naik >2%
   🟡 BUY: Prediksi naik 0.5-2%
   ⚪ HOLD: Perubahan <0.5%
   🟠 SELL: Prediksi turun 0.5-2%
   🔴 STRONG SELL: Prediksi turun >2%

========================================
🔧 TROUBLESHOOTING

MASALAH: "Python was not found"
SOLUSI: 
- Install Python dari python.org
- Atau gunakan run_with_python313.bat
- Atau tambahkan Python ke PATH

MASALAH: "Module not found"
SOLUSI:
- pip install -r requirements.txt
- Atau gunakan: pip install --user -r requirements.txt

MASALAH: "Memory Error" atau laptop hang
SOLUSI:
- Tutup aplikasi lain
- Gunakan konfigurasi minimal
- Kurangi epochs dan batch_size
- Restart laptop jika perlu

MASALAH: "No data available"
SOLUSI:
- Periksa koneksi internet
- Coba lagi beberapa saat kemudian
- Yahoo Finance mungkin sedang maintenance

MASALAH: Training sangat lambat
SOLUSI:
- Gunakan quick_demo.py untuk testing
- Kurangi epochs menjadi 10-15
- Kurangi hidden_size menjadi 16
- Gunakan period 6mo atau 1y

========================================
⚠️ PENTING - DISCLAIMER

1. TUJUAN EDUKASI:
   - Sistem ini untuk pembelajaran dan penelitian
   - BUKAN untuk saran investasi

2. RISIKO INVESTASI:
   - Cryptocurrency sangat berisiko
   - Harga bisa naik/turun drastis
   - Jangan investasi lebih dari kemampuan

3. AKURASI PREDIKSI:
   - Model AI tidak 100% akurat
   - Past performance ≠ future results
   - Selalu lakukan riset sendiri (DYOR)

========================================
📞 BANTUAN TAMBAHAN

Jika masih mengalami masalah:

1. Baca error message dengan teliti
2. Cek apakah semua file ada di folder
3. Pastikan koneksi internet stabil
4. Coba restart Command Prompt/PowerShell
5. Gunakan test_system.py untuk diagnosa

========================================
🎯 TIPS PENGGUNAAN

1. PERTAMA KALI:
   - Jalankan test_system.py dulu
   - Lalu coba quick_demo.py
   - Baru jalankan main.py

2. UNTUK HASIL TERBAIK:
   - Tutup aplikasi lain saat training
   - Gunakan data period 2y
   - Biarkan training selesai tanpa interupsi

3. INTERPRETASI HASIL:
   - RMSE rendah = prediksi lebih akurat
   - Direction accuracy >60% = bagus
   - Lihat grafik untuk pola prediksi

========================================
Happy Trading! 🚀📈

INGAT: Ini hanya untuk edukasi!
Selalu DYOR (Do Your Own Research)
========================================
