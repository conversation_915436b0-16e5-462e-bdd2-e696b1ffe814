========================================
PROJECT STRUCTURE
Cryptocurrency Price Prediction System
========================================

📁 STRUKTUR FILE LENGKAP

🚀 LAUNCHER FILES (<PERSON><PERSON> dari sini):
├── START_HERE.bat                 # 🎯 MAIN LAUNCHER - Mulai dari sini!
├── run_crypto_predictor.bat       # Script otomatis dengan menu
├── run_crypto_predictor.ps1       # PowerShell version (alternatif)
└── run_with_python313.bat         # Untuk Python path spesifik

📚 DOKUMENTASI:
├── QUICK_START.txt               # 🚀 Panduan cepat memulai
├── INSTALL_PYTHON.txt            # 🐍 Cara install Python
├── PANDUAN_PENGGUNAAN.txt        # 📖 Panduan lengkap bahasa Indonesia
├── README.md                     # 📄 Dokumentasi teknis lengkap
└── PROJECT_STRUCTURE.txt         # 📁 File ini (struktur project)

🧪 TESTING & DEMO:
├── test_system.py                # 🔍 Test semua komponen sistem
├── quick_demo.py                 # ⚡ Demo cepat BTC saja
└── main.py                       # 🎯 Program utama (analisis lengkap)

🤖 CORE SYSTEM FILES:
├── crypto_predictor.py           # 🧠 Main predictor class
├── data_fetcher.py              # 📊 Mengambil data dari Yahoo Finance
├── data_preprocessor.py         # 🔧 Preprocessing data untuk ML
├── lstm_model.py                # 🤖 Custom LSTM implementation
├── evaluator.py                 # 📈 Evaluasi model & visualisasi
└── requirements.txt             # 📦 Python dependencies

========================================
🎯 CARA PENGGUNAAN BERDASARKAN KEBUTUHAN

👶 PEMULA / PERTAMA KALI:
1. Baca: QUICK_START.txt
2. Jalankan: START_HERE.bat
3. Pilih: "Test System"

⚡ INGIN COBA CEPAT:
1. Jalankan: START_HERE.bat
2. Pilih: "Quick Demo"
3. Tunggu 5 menit

🎓 ANALISIS LENGKAP:
1. Jalankan: START_HERE.bat
2. Pilih: "Full Analysis"
3. Tunggu 30 menit

🔧 TROUBLESHOOTING:
1. Baca: INSTALL_PYTHON.txt (jika Python belum ada)
2. Jalankan: test_system.py (cek komponen)
3. Baca: PANDUAN_PENGGUNAAN.txt (panduan detail)

========================================
📊 CRYPTOCURRENCY YANG DIDUKUNG

✅ Supported:
- BTC (Bitcoin)
- ETH (Ethereum)
- SOL (Solana)
- ADA (Cardano)
- XRP (Ripple)

📈 Features:
- Real-time data dari Yahoo Finance
- LSTM prediction model
- Trading signals (BUY/SELL/HOLD)
- Performance evaluation
- Grafik visualisasi

========================================
⚙️ KONFIGURASI SISTEM

🖥️ OPTIMIZED UNTUK:
- Core i3 Gen 11 + 16GB RAM
- Windows 10/11
- Python 3.11/3.12

🔧 KONFIGURASI DEFAULT:
- Window Size: 60 hari
- Hidden Layer: 32 neurons
- Epochs: 30
- Batch Size: 16
- Learning Rate: 0.001

========================================
📈 OUTPUT YANG DIHASILKAN

📊 CONSOLE OUTPUT:
- Progress training untuk setiap crypto
- Metrics evaluasi (RMSE, MAE, MAPE)
- Trading signals dengan emoji
- Laporan ringkasan performa

📁 FILE OUTPUT:
- [CRYPTO]_test_prediction_plot.png
- Contoh: BTC_test_prediction_plot.png

🎯 TRADING SIGNALS:
- 🟢 STRONG BUY: Prediksi naik >2%
- 🟡 BUY: Prediksi naik 0.5-2%
- ⚪ HOLD: Perubahan <0.5%
- 🟠 SELL: Prediksi turun 0.5-2%
- 🔴 STRONG SELL: Prediksi turun >2%

========================================
🔒 KEAMANAN & DISCLAIMER

⚠️ PENTING:
- Sistem ini untuk EDUKASI dan PENELITIAN
- BUKAN untuk saran investasi finansial
- Cryptocurrency sangat berisiko
- Selalu DYOR (Do Your Own Research)

🔐 PRIVACY:
- Tidak ada data pribadi yang dikumpulkan
- Hanya menggunakan data publik dari Yahoo Finance
- Semua perhitungan dilakukan secara lokal

========================================
🚀 QUICK COMMANDS

Untuk pengguna advanced yang ingin langsung:

# Test sistem
python test_system.py

# Demo cepat
python quick_demo.py

# Analisis lengkap
python main.py

# Install dependencies manual
pip install -r requirements.txt

========================================
📞 SUPPORT & TROUBLESHOOTING

🔍 LANGKAH TROUBLESHOOTING:
1. Pastikan Python terinstall (python --version)
2. Install dependencies (pip install -r requirements.txt)
3. Test komponen (python test_system.py)
4. Cek koneksi internet
5. Baca error message dengan teliti

📚 DOKUMENTASI:
- QUICK_START.txt → Panduan cepat
- INSTALL_PYTHON.txt → Install Python
- PANDUAN_PENGGUNAAN.txt → Panduan lengkap
- README.md → Dokumentasi teknis

========================================
🎉 SELAMAT MENGGUNAKAN!

Mulai dengan: START_HERE.bat
Happy Trading! 🚀📈

INGAT: Ini hanya untuk edukasi!
========================================
