# PowerShell Script untuk Cryptocurrency Price Prediction System
# Lebih robust daripada batch file

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Cryptocurrency Price Prediction System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Mencari Python di sistem..." -ForegroundColor Yellow
Write-Host ""

# Daftar lokasi Python yang mungkin
$pythonPaths = @(
    "python",
    "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe",
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe",
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe",
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe",
    "C:\Python313\python.exe",
    "C:\Python312\python.exe",
    "C:\Python311\python.exe"
)

$pythonCmd = $null

# Cari Python yang tersedia
foreach ($path in $pythonPaths) {
    try {
        $result = & $path --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Python ditemukan: $path" -ForegroundColor Green
            Write-Host "   Version: $result" -ForegroundColor Gray
            $pythonCmd = $path
            break
        }
    }
    catch {
        # Lanjut ke path berikutnya
    }
}

if (-not $pythonCmd) {
    Write-Host "❌ Python tidak ditemukan di lokasi manapun!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Silakan install Python dari: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host "Atau pastikan Python sudah terinstall dengan benar." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Lokasi yang dicoba:" -ForegroundColor Gray
    foreach ($path in $pythonPaths) {
        Write-Host "- $path" -ForegroundColor Gray
    }
    Read-Host "Tekan Enter untuk keluar"
    exit 1
}

Write-Host ""

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
try {
    & $pythonCmd -m pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ Error installing dependencies dengan pip biasa" -ForegroundColor Yellow
        Write-Host "Mencoba dengan --user flag..." -ForegroundColor Yellow
        & $pythonCmd -m pip install --user -r requirements.txt
        if ($LASTEXITCODE -ne 0) {
            throw "Gagal install dependencies"
        }
    }
    Write-Host "✅ Dependencies berhasil diinstall!" -ForegroundColor Green
}
catch {
    Write-Host "❌ Gagal install dependencies" -ForegroundColor Red
    Write-Host "Coba manual: pip install numpy pandas matplotlib yfinance scikit-learn scipy" -ForegroundColor Yellow
    Read-Host "Tekan Enter untuk keluar"
    exit 1
}

Write-Host ""

# Menu pilihan
do {
    Write-Host "🚀 Pilih mode yang ingin dijalankan:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. Test System (Recommended untuk pertama kali)" -ForegroundColor White
    Write-Host "2. Quick Demo (Demo cepat BTC saja)" -ForegroundColor White
    Write-Host "3. Full Analysis (Analisis lengkap semua crypto)" -ForegroundColor White
    Write-Host "4. Exit" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Pilih (1-4)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "🔍 Menjalankan Test System..." -ForegroundColor Yellow
            & $pythonCmd test_system.py
            break
        }
        "2" {
            Write-Host ""
            Write-Host "⚡ Menjalankan Quick Demo..." -ForegroundColor Yellow
            & $pythonCmd quick_demo.py
            break
        }
        "3" {
            Write-Host ""
            Write-Host "🎯 Menjalankan Full Analysis..." -ForegroundColor Yellow
            Write-Host "⚠️ Ini akan memakan waktu 15-30 menit" -ForegroundColor Red
            $confirm = Read-Host "Lanjutkan? (y/n)"
            if ($confirm -eq "y" -or $confirm -eq "Y") {
                & $pythonCmd main.py
            } else {
                Write-Host "Dibatalkan." -ForegroundColor Yellow
            }
            break
        }
        "4" {
            Write-Host "Goodbye!" -ForegroundColor Green
            exit 0
        }
        default {
            Write-Host "Pilihan tidak valid. Menjalankan Test System..." -ForegroundColor Yellow
            & $pythonCmd test_system.py
            break
        }
    }
    
    Write-Host ""
    Write-Host "✅ Selesai!" -ForegroundColor Green
    $continue = Read-Host "Jalankan lagi? (y/n)"
    
} while ($continue -eq "y" -or $continue -eq "Y")

Write-Host "👋 Terima kasih telah menggunakan Crypto Prediction System!" -ForegroundColor Cyan
