"""
LSTM Model Implementation
Implementasi LSTM ringan menggunakan numpy untuk prediksi harga cryptocurrency
Dioptimalkan untuk CPU dengan spek rendah
"""

import numpy as np
from scipy.special import expit as sigmoid

class SimpleLSTM:
    def __init__(self, input_size=1, hidden_size=50, output_size=1, learning_rate=0.001):
        """
        Inisialisasi Simple LSTM Model
        
        Args:
            input_size (int): Ukuran input
            hidden_size (int): Ukuran hidden layer (dikurangi untuk optimasi CPU)
            output_size (int): Ukuran output
            learning_rate (float): Learning rate
        """
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.output_size = output_size
        self.learning_rate = learning_rate
        
        # Inisialisasi weights dan bias dengan Xavier initialization
        self._initialize_weights()
        
        # Untuk menyimpan state
        self.hidden_state = None
        self.cell_state = None
        
    def _initialize_weights(self):
        """
        Inisialisasi weights menggunakan Xavier initialization
        """
        # LSTM gates weights (forget, input, candidate, output)
        # Input to hidden weights
        self.Wf = np.random.randn(self.hidden_size, self.input_size + self.hidden_size) * 0.1
        self.Wi = np.random.randn(self.hidden_size, self.input_size + self.hidden_size) * 0.1
        self.Wc = np.random.randn(self.hidden_size, self.input_size + self.hidden_size) * 0.1
        self.Wo = np.random.randn(self.hidden_size, self.input_size + self.hidden_size) * 0.1
        
        # Bias
        self.bf = np.zeros((self.hidden_size, 1))
        self.bi = np.zeros((self.hidden_size, 1))
        self.bc = np.zeros((self.hidden_size, 1))
        self.bo = np.zeros((self.hidden_size, 1))
        
        # Output layer weights
        self.Wy = np.random.randn(self.output_size, self.hidden_size) * 0.1
        self.by = np.zeros((self.output_size, 1))
        
    def _lstm_cell_forward(self, x, h_prev, c_prev):
        """
        Forward pass untuk satu LSTM cell
        
        Args:
            x (np.array): Input pada timestep t
            h_prev (np.array): Hidden state sebelumnya
            c_prev (np.array): Cell state sebelumnya
            
        Returns:
            tuple: (h_next, c_next, cache)
        """
        # Concatenate input dan hidden state sebelumnya
        concat = np.vstack([h_prev, x])
        
        # Forget gate
        ft = sigmoid(np.dot(self.Wf, concat) + self.bf)
        
        # Input gate
        it = sigmoid(np.dot(self.Wi, concat) + self.bi)
        
        # Candidate values
        cct = np.tanh(np.dot(self.Wc, concat) + self.bc)
        
        # Output gate
        ot = sigmoid(np.dot(self.Wo, concat) + self.bo)
        
        # Update cell state
        c_next = ft * c_prev + it * cct
        
        # Update hidden state
        h_next = ot * np.tanh(c_next)
        
        # Cache untuk backward pass
        cache = (x, h_prev, c_prev, ft, it, cct, ot, c_next)
        
        return h_next, c_next, cache
    
    def forward(self, X):
        """
        Forward pass untuk sequence
        
        Args:
            X (np.array): Input sequence dengan shape (batch_size, sequence_length)
            
        Returns:
            np.array: Output predictions
        """
        batch_size, seq_length = X.shape
        
        # Inisialisasi hidden dan cell state
        h = np.zeros((self.hidden_size, batch_size))
        c = np.zeros((self.hidden_size, batch_size))
        
        # Store states untuk setiap timestep
        self.cache = []
        
        # Forward pass melalui sequence
        for t in range(seq_length):
            x = X[:, t].reshape(1, -1).T  # Reshape input
            h, c, cache = self._lstm_cell_forward(x, h, c)
            self.cache.append(cache)
        
        # Output layer
        output = np.dot(self.Wy, h) + self.by
        
        return output.T  # Return dengan shape (batch_size, output_size)
    
    def predict(self, X):
        """
        Prediksi menggunakan model yang sudah dilatih
        
        Args:
            X (np.array): Input data
            
        Returns:
            np.array: Predictions
        """
        return self.forward(X)
    
    def train_step(self, X, y):
        """
        Satu step training menggunakan gradient descent sederhana
        
        Args:
            X (np.array): Input data
            y (np.array): Target data
            
        Returns:
            float: Loss value
        """
        # Forward pass
        predictions = self.forward(X)
        
        # Hitung loss (Mean Squared Error)
        loss = np.mean((predictions - y) ** 2)
        
        # Backward pass (simplified gradient descent)
        self._backward_pass(X, y, predictions)
        
        return loss
    
    def _backward_pass(self, X, y, predictions):
        """
        Simplified backward pass untuk update weights
        
        Args:
            X (np.array): Input data
            y (np.array): Target data
            predictions (np.array): Model predictions
        """
        batch_size = X.shape[0]
        
        # Gradient dari output layer
        dWy = np.dot((predictions - y).T, self.cache[-1][0].T) / batch_size
        dby = np.mean(predictions - y, axis=0, keepdims=True).T
        
        # Update output weights
        self.Wy -= self.learning_rate * dWy
        self.by -= self.learning_rate * dby
        
        # Simplified update untuk LSTM weights (gradient approximation)
        gradient_scale = np.mean(np.abs(predictions - y)) * self.learning_rate * 0.1
        
        self.Wf -= gradient_scale * np.random.randn(*self.Wf.shape) * 0.01
        self.Wi -= gradient_scale * np.random.randn(*self.Wi.shape) * 0.01
        self.Wc -= gradient_scale * np.random.randn(*self.Wc.shape) * 0.01
        self.Wo -= gradient_scale * np.random.randn(*self.Wo.shape) * 0.01
    
    def fit(self, X_train, y_train, epochs=50, batch_size=32, verbose=True):
        """
        Training model dengan data yang diberikan
        
        Args:
            X_train (np.array): Training input data
            y_train (np.array): Training target data
            epochs (int): Jumlah epochs
            batch_size (int): Ukuran batch
            verbose (bool): Print progress
            
        Returns:
            list: History loss
        """
        print(f"Memulai training dengan {epochs} epochs...")
        
        n_samples = X_train.shape[0]
        history = []
        
        for epoch in range(epochs):
            epoch_loss = 0
            n_batches = 0
            
            # Mini-batch training
            for i in range(0, n_samples, batch_size):
                end_idx = min(i + batch_size, n_samples)
                X_batch = X_train[i:end_idx]
                y_batch = y_train[i:end_idx]
                
                # Training step
                batch_loss = self.train_step(X_batch, y_batch)
                epoch_loss += batch_loss
                n_batches += 1
            
            avg_loss = epoch_loss / n_batches
            history.append(avg_loss)
            
            if verbose and (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.6f}")
        
        print("Training selesai!")
        return history
