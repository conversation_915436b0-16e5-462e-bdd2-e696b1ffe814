@echo off
echo ========================================
echo Cryptocurrency Price Prediction System
echo ========================================
echo Using Python 3.13 from user directory
echo.

REM Set Python path (sesuai dengan lokasi Python Anda)
set PYTHON_PATH="C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13\python.exe"

REM Check if Python exists at the specified path
if not exist %PYTHON_PATH% (
    echo Python tidak ditemukan di path: %PYTHON_PATH%
    echo Silakan sesuaikan path Python di file ini atau install Python
    echo.
    pause
    exit /b 1
)

echo Python ditemukan!
%PYTHON_PATH% --version
echo.

REM Install dependencies
echo Installing dependencies...
%PYTHON_PATH% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Error installing dependencies!
    echo Trying with --user flag...
    %PYTHON_PATH% -m pip install --user -r requirements.txt
)

echo.
echo Dependencies installation completed!
echo.

REM Run the main program
echo Starting Cryptocurrency Price Prediction System...
echo.
%PYTHON_PATH% main.py

pause
