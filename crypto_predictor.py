"""
Crypto Predictor Main Class
Menggabungkan semua komponen untuk prediksi harga cryptocurrency
"""

import numpy as np
import pandas as pd
import time
from datetime import datetime

from data_fetcher import CryptoDataFetcher
from data_preprocessor import CryptoDataPreprocessor
from lstm_model import SimpleLS<PERSON>
from evaluator import ModelEvaluator

class CryptoPricePredictor:
    def __init__(self, window_size=60, hidden_size=32, learning_rate=0.001):
        """
        Inisialisasi Crypto Price Predictor
        
        Args:
            window_size (int): Jumlah hari untuk input sequence
            hidden_size (int): Ukuran hidden layer LSTM (dikurangi untuk optimasi)
            learning_rate (float): Learning rate untuk training
        """
        self.window_size = window_size
        self.hidden_size = hidden_size
        self.learning_rate = learning_rate
        
        # Inisialisasi komponen
        self.data_fetcher = CryptoDataFetcher()
        self.preprocessor = CryptoDataPreprocessor(window_size=window_size)
        self.evaluator = ModelEvaluator()
        
        # Dictionary untuk menyimpan model setiap cryptocurrency
        self.models = {}
        self.crypto_data = {}
        
        # List cryptocurrency yang didukung
        self.supported_cryptos = ['BTC', 'ETH', 'SOL', 'ADA', 'XRP']
        
    def fetch_all_data(self, period='2y'):
        """
        Mengambil data untuk semua cryptocurrency
        
        Args:
            period (str): Periode data yang diambil
        """
        print("🔄 Mengambil data cryptocurrency...")
        self.crypto_data = self.data_fetcher.fetch_all_crypto_data(period=period)
        
        # Validasi data
        valid_cryptos = []
        for symbol, data in self.crypto_data.items():
            if self.data_fetcher.validate_data(data, min_days=self.window_size + 50):
                valid_cryptos.append(symbol)
                print(f"✅ Data {symbol}: {len(data)} hari")
            else:
                print(f"❌ Data {symbol} tidak valid atau terlalu sedikit")
        
        # Update list cryptocurrency yang valid
        self.supported_cryptos = valid_cryptos
        print(f"\n📊 Total cryptocurrency valid: {len(self.supported_cryptos)}")
    
    def train_model_for_crypto(self, symbol, epochs=30, batch_size=16):
        """
        Melatih model untuk satu cryptocurrency
        
        Args:
            symbol (str): Symbol cryptocurrency
            epochs (int): Jumlah epochs training (dikurangi untuk optimasi)
            batch_size (int): Ukuran batch (dikurangi untuk optimasi)
            
        Returns:
            dict: Hasil training dan evaluasi
        """
        if symbol not in self.crypto_data:
            print(f"❌ Data untuk {symbol} tidak tersedia")
            return None
        
        print(f"\n🚀 Memulai training model untuk {symbol}...")
        start_time = time.time()
        
        # Preprocessing data
        data = self.crypto_data[symbol]
        X_train, y_train, X_test, y_test, scaled_data = self.preprocessor.prepare_data(data)
        
        # Inisialisasi model LSTM
        model = SimpleLSTM(
            input_size=1,
            hidden_size=self.hidden_size,
            output_size=1,
            learning_rate=self.learning_rate
        )
        
        # Training model
        print(f"📚 Training {symbol} dengan {len(X_train)} samples...")
        history = model.fit(X_train, y_train, epochs=epochs, batch_size=batch_size, verbose=True)
        
        # Evaluasi model
        print(f"🔍 Evaluasi model {symbol}...")
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)
        
        # Denormalisasi prediksi
        y_train_actual = self.preprocessor.inverse_transform(y_train).flatten()
        y_test_actual = self.preprocessor.inverse_transform(y_test).flatten()
        y_pred_train_actual = self.preprocessor.inverse_transform(y_pred_train).flatten()
        y_pred_test_actual = self.preprocessor.inverse_transform(y_pred_test).flatten()
        
        # Evaluasi dengan evaluator
        train_results = self.evaluator.evaluate_model(y_train_actual, y_pred_train_actual, f"{symbol}_train")
        test_results = self.evaluator.evaluate_model(y_test_actual, y_pred_test_actual, f"{symbol}_test")
        
        # Simpan model
        self.models[symbol] = {
            'model': model,
            'preprocessor': self.preprocessor,
            'train_results': train_results,
            'test_results': test_results,
            'history': history
        }
        
        training_time = time.time() - start_time
        print(f"⏱️ Training {symbol} selesai dalam {training_time:.2f} detik")
        
        return {
            'symbol': symbol,
            'train_results': train_results,
            'test_results': test_results,
            'training_time': training_time
        }
    
    def train_all_models(self, epochs=30, batch_size=16):
        """
        Melatih model untuk semua cryptocurrency
        
        Args:
            epochs (int): Jumlah epochs
            batch_size (int): Ukuran batch
        """
        print(f"\n🎯 Memulai training untuk {len(self.supported_cryptos)} cryptocurrency...")
        total_start_time = time.time()
        
        training_results = []
        
        for i, symbol in enumerate(self.supported_cryptos, 1):
            print(f"\n{'='*50}")
            print(f"📈 Training {i}/{len(self.supported_cryptos)}: {symbol}")
            print(f"{'='*50}")
            
            result = self.train_model_for_crypto(symbol, epochs=epochs, batch_size=batch_size)
            if result:
                training_results.append(result)
                
                # Buat plot untuk cryptocurrency ini
                self.evaluator.plot_predictions(f"{symbol}_test", save_plot=True)
        
        total_time = time.time() - total_start_time
        print(f"\n🎉 Semua training selesai dalam {total_time:.2f} detik")
        print(f"⚡ Rata-rata waktu per crypto: {total_time/len(self.supported_cryptos):.2f} detik")
        
        # Buat laporan ringkasan
        self.evaluator.create_summary_report()
        
        return training_results
    
    def predict_next_day(self, symbol):
        """
        Prediksi harga untuk hari berikutnya
        
        Args:
            symbol (str): Symbol cryptocurrency
            
        Returns:
            dict: Informasi prediksi
        """
        if symbol not in self.models:
            print(f"❌ Model untuk {symbol} belum dilatih")
            return None
        
        # Ambil data terbaru
        recent_data = self.crypto_data[symbol]
        current_price = recent_data['Close'].iloc[-1]
        
        # Persiapkan input untuk prediksi
        prediction_input = self.preprocessor.prepare_prediction_input(recent_data)
        
        # Prediksi
        model = self.models[symbol]['model']
        scaled_prediction = model.predict(prediction_input)
        
        # Denormalisasi
        predicted_price = self.preprocessor.inverse_transform(scaled_prediction)[0, 0]
        
        return {
            'symbol': symbol,
            'current_price': current_price,
            'predicted_price': predicted_price,
            'price_change': predicted_price - current_price,
            'price_change_pct': ((predicted_price - current_price) / current_price) * 100
        }
    
    def generate_trading_signals(self):
        """
        Generate sinyal trading untuk semua cryptocurrency
        
        Returns:
            list: List sinyal trading
        """
        print("\n🎯 Generating trading signals...")
        signals = []
        
        for symbol in self.supported_cryptos:
            if symbol in self.models:
                prediction = self.predict_next_day(symbol)
                if prediction:
                    signal = self.evaluator.generate_trading_signal(
                        prediction['current_price'],
                        prediction['predicted_price'],
                        symbol
                    )
                    signals.append(signal)
        
        # Print sinyal
        self.evaluator.print_trading_signals(signals)
        
        return signals
    
    def run_full_analysis(self, period='2y', epochs=30, batch_size=16):
        """
        Menjalankan analisis lengkap
        
        Args:
            period (str): Periode data
            epochs (int): Jumlah epochs training
            batch_size (int): Ukuran batch
        """
        print("🚀 Memulai analisis lengkap cryptocurrency...")
        print(f"⚙️ Konfigurasi: Window={self.window_size}, Hidden={self.hidden_size}, LR={self.learning_rate}")
        
        # 1. Fetch data
        self.fetch_all_data(period=period)
        
        if not self.supported_cryptos:
            print("❌ Tidak ada data cryptocurrency yang valid")
            return
        
        # 2. Train models
        training_results = self.train_all_models(epochs=epochs, batch_size=batch_size)
        
        # 3. Generate trading signals
        signals = self.generate_trading_signals()
        
        print("\n✅ Analisis lengkap selesai!")
        return {
            'training_results': training_results,
            'trading_signals': signals
        }
