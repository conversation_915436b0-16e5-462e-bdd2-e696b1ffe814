"""
Evaluator <PERSON><PERSON><PERSON> model dan visualisasi hasil prediksi
"""

import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

class ModelEvaluator:
    def __init__(self):
        """
        Inisialisasi evaluator
        """
        self.results = {}
        
    def calculate_rmse(self, y_true, y_pred):
        """
        Menghitung Root Mean Square Error (RMSE)
        
        Args:
            y_true (np.array): Nilai aktual
            y_pred (np.array): <PERSON><PERSON> prediksi
            
        Returns:
            float: RMSE value
        """
        mse = np.mean((y_true - y_pred) ** 2)
        rmse = np.sqrt(mse)
        return rmse
    
    def calculate_mae(self, y_true, y_pred):
        """
        Menghitung Mean Absolute Error (MAE)
        
        Args:
            y_true (np.array): <PERSON>lai aktual
            y_pred (np.array): <PERSON><PERSON> prediksi
            
        Returns:
            float: MAE value
        """
        mae = np.mean(np.abs(y_true - y_pred))
        return mae
    
    def calculate_mape(self, y_true, y_pred):
        """
        Menghitung Mean Absolute Percentage Error (MAPE)
        
        Args:
            y_true (np.array): <PERSON><PERSON> aktual
            y_pred (np.array): Nilai prediksi
            
        Returns:
            float: MAPE value (dalam persen)
        """
        # Hindari pembagian dengan nol
        mask = y_true != 0
        mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
        return mape
    
    def calculate_accuracy_direction(self, y_true, y_pred):
        """
        Menghitung akurasi arah pergerakan (naik/turun)
        
        Args:
            y_true (np.array): Nilai aktual
            y_pred (np.array): Nilai prediksi
            
        Returns:
            float: Akurasi arah dalam persen
        """
        if len(y_true) < 2 or len(y_pred) < 2:
            return 0.0
        
        # Hitung perubahan arah
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        
        # Hitung akurasi
        correct_direction = np.sum(true_direction == pred_direction)
        accuracy = (correct_direction / len(true_direction)) * 100
        
        return accuracy
    
    def evaluate_model(self, y_true, y_pred, symbol):
        """
        Evaluasi lengkap model untuk satu cryptocurrency
        
        Args:
            y_true (np.array): Nilai aktual
            y_pred (np.array): Nilai prediksi
            symbol (str): Symbol cryptocurrency
            
        Returns:
            dict: Dictionary dengan metrics evaluasi
        """
        # Flatten arrays jika perlu
        y_true = y_true.flatten()
        y_pred = y_pred.flatten()
        
        # Hitung berbagai metrics
        rmse = self.calculate_rmse(y_true, y_pred)
        mae = self.calculate_mae(y_true, y_pred)
        mape = self.calculate_mape(y_true, y_pred)
        direction_accuracy = self.calculate_accuracy_direction(y_true, y_pred)
        
        # Simpan hasil
        results = {
            'symbol': symbol,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_accuracy,
            'y_true': y_true,
            'y_pred': y_pred
        }
        
        self.results[symbol] = results
        
        # Print hasil evaluasi
        print(f"\n=== Evaluasi Model {symbol} ===")
        print(f"RMSE: ${rmse:.4f}")
        print(f"MAE: ${mae:.4f}")
        print(f"MAPE: {mape:.2f}%")
        print(f"Akurasi Arah: {direction_accuracy:.2f}%")
        
        return results
    
    def plot_predictions(self, symbol, save_plot=True):
        """
        Membuat plot perbandingan prediksi vs aktual
        
        Args:
            symbol (str): Symbol cryptocurrency
            save_plot (bool): Simpan plot ke file
        """
        if symbol not in self.results:
            print(f"Tidak ada hasil untuk {symbol}")
            return
        
        results = self.results[symbol]
        y_true = results['y_true']
        y_pred = results['y_pred']
        
        # Buat plot
        plt.figure(figsize=(12, 6))
        
        # Plot data
        plt.plot(y_true, label='Harga Aktual', color='blue', linewidth=2)
        plt.plot(y_pred, label='Prediksi', color='red', linewidth=2, alpha=0.8)
        
        # Styling
        plt.title(f'Prediksi vs Aktual - {symbol}', fontsize=16, fontweight='bold')
        plt.xlabel('Hari', fontsize=12)
        plt.ylabel('Harga (USD)', fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Tambahkan informasi metrics
        rmse = results['rmse']
        mape = results['mape']
        direction_acc = results['direction_accuracy']
        
        info_text = f'RMSE: ${rmse:.4f}\nMAPE: {mape:.2f}%\nAkurasi Arah: {direction_acc:.2f}%'
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_plot:
            filename = f'{symbol}_prediction_plot.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Plot disimpan sebagai {filename}")
        
        plt.show()
    
    def generate_trading_signal(self, current_price, predicted_price, symbol):
        """
        Generate sinyal trading sederhana
        
        Args:
            current_price (float): Harga saat ini
            predicted_price (float): Harga prediksi
            symbol (str): Symbol cryptocurrency
            
        Returns:
            dict: Dictionary dengan sinyal dan informasi
        """
        price_change = predicted_price - current_price
        price_change_pct = (price_change / current_price) * 100
        
        # Tentukan sinyal
        if price_change_pct > 2:  # Naik lebih dari 2%
            signal = "STRONG BUY"
            color = "🟢"
        elif price_change_pct > 0.5:  # Naik 0.5-2%
            signal = "BUY"
            color = "🟡"
        elif price_change_pct < -2:  # Turun lebih dari 2%
            signal = "STRONG SELL"
            color = "🔴"
        elif price_change_pct < -0.5:  # Turun 0.5-2%
            signal = "SELL"
            color = "🟠"
        else:  # Perubahan kecil
            signal = "HOLD"
            color = "⚪"
        
        signal_info = {
            'symbol': symbol,
            'current_price': current_price,
            'predicted_price': predicted_price,
            'price_change': price_change,
            'price_change_pct': price_change_pct,
            'signal': signal,
            'color': color
        }
        
        return signal_info
    
    def print_trading_signals(self, signals):
        """
        Print sinyal trading dalam format yang rapi
        
        Args:
            signals (list): List of trading signals
        """
        print("\n" + "="*60)
        print("🚀 SINYAL TRADING CRYPTOCURRENCY 🚀")
        print("="*60)
        
        for signal in signals:
            print(f"{signal['color']} {signal['symbol']:<6} | "
                  f"Saat ini: ${signal['current_price']:.4f} | "
                  f"Prediksi: ${signal['predicted_price']:.4f} | "
                  f"Perubahan: {signal['price_change_pct']:+.2f}% | "
                  f"Sinyal: {signal['signal']}")
        
        print("="*60)
        print("📊 Keterangan:")
        print("🟢 STRONG BUY: Prediksi naik >2%")
        print("🟡 BUY: Prediksi naik 0.5-2%")
        print("⚪ HOLD: Perubahan <0.5%")
        print("🟠 SELL: Prediksi turun 0.5-2%")
        print("🔴 STRONG SELL: Prediksi turun >2%")
        print("="*60)
    
    def create_summary_report(self):
        """
        Membuat laporan ringkasan untuk semua cryptocurrency
        """
        if not self.results:
            print("Tidak ada hasil untuk dibuat laporan")
            return
        
        print("\n" + "="*80)
        print("📈 LAPORAN RINGKASAN EVALUASI MODEL")
        print("="*80)
        
        # Header tabel
        print(f"{'Symbol':<8} {'RMSE':<12} {'MAE':<12} {'MAPE (%)':<12} {'Akurasi Arah (%)':<18}")
        print("-" * 80)
        
        # Data untuk setiap cryptocurrency
        for symbol, results in self.results.items():
            print(f"{symbol:<8} ${results['rmse']:<11.4f} ${results['mae']:<11.4f} "
                  f"{results['mape']:<11.2f} {results['direction_accuracy']:<17.2f}")
        
        print("="*80)
        
        # Statistik keseluruhan
        all_rmse = [results['rmse'] for results in self.results.values()]
        all_mape = [results['mape'] for results in self.results.values()]
        all_direction = [results['direction_accuracy'] for results in self.results.values()]
        
        print(f"\n📊 Statistik Keseluruhan:")
        print(f"Rata-rata RMSE: ${np.mean(all_rmse):.4f}")
        print(f"Rata-rata MAPE: {np.mean(all_mape):.2f}%")
        print(f"Rata-rata Akurasi Arah: {np.mean(all_direction):.2f}%")
        print("="*80)
