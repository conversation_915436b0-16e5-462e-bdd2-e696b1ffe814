========================================
QUICK START GUIDE
Cryptocurrency Price Prediction System
========================================

🚀 CARA CEPAT MEMULAI

1. PASTIKAN PYTHON TERINSTALL
   - Jika belum ada Python, baca: INSTALL_PYTHON.txt
   - Test dengan buka Command Prompt, ketik: python --version

2. JALANKAN SISTEM
   - Double-click: START_HERE.bat
   - Atau double-click: run_crypto_predictor.bat
   - Pilih menu yang tersedia

3. PILIHAN MENU
   - Test System: Cek semua komponen (RECOMMENDED pertama kali)
   - Quick Demo: Demo cepat BTC saja (~5 menit)
   - Full Analysis: Analisis lengkap semua crypto (~30 menit)

========================================
📁 FILE PENTING

UNTUK MENJALANKAN:
- START_HERE.bat ← MULAI DARI SINI
- run_crypto_predictor.bat
- run_crypto_predictor.ps1 (PowerShell)

UNTUK TESTING:
- test_system.py
- quick_demo.py

DOKUMENTASI:
- README.md (dokumentasi lengkap)
- PANDUAN_PENGGUNAAN.txt (panduan detail)
- INSTALL_PYTHON.txt (cara install Python)

========================================
⚡ JIKA TERBURU-BURU

1. Double-click: START_HERE.bat
2. Pilih "2" untuk Quick Demo
3. Tunggu 5 menit
4. Lihat hasil prediksi BTC

========================================
🔧 JIKA ADA MASALAH

1. Python belum terinstall → Baca INSTALL_PYTHON.txt
2. Error saat running → Jalankan test_system.py dulu
3. Laptop lambat → Gunakan Quick Demo, jangan Full Analysis
4. Butuh bantuan → Baca PANDUAN_PENGGUNAAN.txt

========================================
📊 OUTPUT YANG DIHASILKAN

- Console: Progress training, metrics, trading signals
- File PNG: Grafik prediksi vs harga aktual
- Trading signals: BUY/SELL/HOLD recommendations

========================================
⚠️ DISCLAIMER

Sistem ini untuk EDUKASI saja, bukan saran investasi!
Cryptocurrency sangat berisiko - invest wisely!

========================================
Happy Trading! 🚀📈
