"""
Data Preprocessor Module
Preprocessing data untuk training model LSTM
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler

class CryptoDataPreprocessor:
    def __init__(self, window_size=60, prediction_days=1):
        """
        Inisialisasi preprocessor
        
        Args:
            window_size (int): <PERSON><PERSON><PERSON> hari untuk input sequence (default: 60)
            prediction_days (int): <PERSON><PERSON><PERSON> hari untuk prediksi (default: 1)
        """
        self.window_size = window_size
        self.prediction_days = prediction_days
        self.scaler = MinMaxScaler(feature_range=(0, 1))
        self.is_fitted = False
        
    def prepare_data(self, data, target_column='Close'):
        """
        Mempersiapkan data untuk training dan testing
        
        Args:
            data (pd.DataFrame): Data historis cryptocurrency
            target_column (str): Ko<PERSON>m yang akan diprediksi
            
        Returns:
            tuple: (X_train, y_train, X_test, y_test, scaled_data)
        """
        print(f"Mempersiapkan data dengan window size: {self.window_size}")
        
        # Ambil kolom target (Close price)
        prices = data[target_column].values.reshape(-1, 1)
        
        # Normalisasi data menggunakan MinMaxScaler
        scaled_prices = self.scaler.fit_transform(prices)
        self.is_fitted = True
        
        # Buat sequences untuk training
        X, y = self._create_sequences(scaled_prices)
        
        # Split data menjadi train dan test (80:20)
        split_index = int(len(X) * 0.8)
        
        X_train = X[:split_index]
        y_train = y[:split_index]
        X_test = X[split_index:]
        y_test = y[split_index:]
        
        print(f"Data training: {len(X_train)} samples")
        print(f"Data testing: {len(X_test)} samples")
        
        return X_train, y_train, X_test, y_test, scaled_prices
    
    def _create_sequences(self, data):
        """
        Membuat sequences dari data time series
        
        Args:
            data (np.array): Data yang sudah dinormalisasi
            
        Returns:
            tuple: (X, y) sequences
        """
        X, y = [], []
        
        for i in range(self.window_size, len(data) - self.prediction_days + 1):
            # Input sequence (60 hari terakhir)
            X.append(data[i-self.window_size:i, 0])
            # Target (1 hari ke depan)
            y.append(data[i:i+self.prediction_days, 0])
        
        return np.array(X), np.array(y)
    
    def inverse_transform(self, scaled_data):
        """
        Mengembalikan data yang sudah dinormalisasi ke skala asli
        
        Args:
            scaled_data (np.array): Data yang sudah dinormalisasi
            
        Returns:
            np.array: Data dalam skala asli
        """
        if not self.is_fitted:
            raise ValueError("Scaler belum di-fit. Jalankan prepare_data terlebih dahulu.")
        
        # Reshape jika perlu
        if scaled_data.ndim == 1:
            scaled_data = scaled_data.reshape(-1, 1)
        
        return self.scaler.inverse_transform(scaled_data)
    
    def add_technical_indicators(self, data):
        """
        Menambahkan indikator teknikal sederhana
        
        Args:
            data (pd.DataFrame): Data historis
            
        Returns:
            pd.DataFrame: Data dengan indikator teknikal
        """
        df = data.copy()
        
        # Simple Moving Average (SMA)
        df['SMA_10'] = df['Close'].rolling(window=10).mean()
        df['SMA_30'] = df['Close'].rolling(window=30).mean()
        
        # Exponential Moving Average (EMA)
        df['EMA_12'] = df['Close'].ewm(span=12).mean()
        df['EMA_26'] = df['Close'].ewm(span=26).mean()
        
        # MACD
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # RSI (Relative Strength Index)
        df['RSI'] = self._calculate_rsi(df['Close'])
        
        # Volatility (rolling standard deviation)
        df['Volatility'] = df['Close'].rolling(window=20).std()
        
        # Price change percentage
        df['Price_Change'] = df['Close'].pct_change()
        
        # Remove NaN values
        df = df.dropna()
        
        return df
    
    def _calculate_rsi(self, prices, window=14):
        """
        Menghitung Relative Strength Index (RSI)
        
        Args:
            prices (pd.Series): Series harga
            window (int): Window untuk perhitungan RSI
            
        Returns:
            pd.Series: RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def prepare_prediction_input(self, recent_data, target_column='Close'):
        """
        Mempersiapkan input untuk prediksi real-time
        
        Args:
            recent_data (pd.DataFrame): Data terbaru (minimal window_size hari)
            target_column (str): Kolom target
            
        Returns:
            np.array: Input yang siap untuk prediksi
        """
        if len(recent_data) < self.window_size:
            raise ValueError(f"Data terlalu sedikit. Butuh minimal {self.window_size} hari")
        
        # Ambil data terakhir sebanyak window_size
        recent_prices = recent_data[target_column].tail(self.window_size).values.reshape(-1, 1)
        
        # Normalisasi menggunakan scaler yang sudah di-fit
        if not self.is_fitted:
            raise ValueError("Scaler belum di-fit. Jalankan prepare_data terlebih dahulu.")
        
        scaled_recent = self.scaler.transform(recent_prices)
        
        # Reshape untuk input model
        prediction_input = scaled_recent.reshape(1, self.window_size)
        
        return prediction_input
