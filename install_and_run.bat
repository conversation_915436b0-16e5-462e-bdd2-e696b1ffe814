@echo off
echo ========================================
echo Cryptocurrency Price Prediction System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python tidak ditemukan!
    echo Silakan install Python terlebih dahulu dari:
    echo https://www.python.org/downloads/
    echo.
    echo Atau gunakan Python yang sudah terinstall di:
    echo C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13
    echo.
    pause
    exit /b 1
)

echo Python ditemukan!
python --version
echo.

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Error installing dependencies!
    pause
    exit /b 1
)

echo.
echo Dependencies installed successfully!
echo.

REM Run the main program
echo Starting Cryptocurrency Price Prediction System...
echo.
python main.py

pause
