# 🚀 Cryptocurrency Price Prediction System

Sistem prediksi harga cryptocurrency menggunakan LSTM yang dioptimalkan untuk laptop dengan spesifikasi rendah (Core i3 + 16GB RAM).

## 📊 Fitur Utama

- **Multi-Cryptocurrency Support**: BTC, ETH, SOL, ADA, XRP
- **Custom LSTM Model**: Implementasi LSTM ringan menggunakan numpy
- **Real-time Data**: Mengambil data dari Yahoo Finance
- **Trading Signals**: Sinyal BUY/SELL berdasarkan prediksi
- **Performance Evaluation**: RMSE, MAE, MAPE, dan akurasi arah
- **Visualisasi**: Grafik perbandingan prediksi vs harga aktual
- **CPU Optimized**: Dioptimalkan untuk laptop spek rendah

## 🛠️ Instalasi

1. **Clone atau download project ini**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Cara Penggunaan

### Menjalankan Analisis <PERSON>
```bash
python main.py
```

### Kon<PERSON>gu<PERSON><PERSON> Default (Optimized untuk Core i3 + 16GB RAM)
- **Data Period**: 2 tahun
- **Window Size**: 60 hari
- **Hidden Layer**: 32 neurons
- **Epochs**: 30
- **Batch Size**: 16
- **Learning Rate**: 0.001

## 📁 Struktur Project

```
├── main.py                 # Entry point aplikasi
├── crypto_predictor.py     # Main predictor class
├── data_fetcher.py         # Module untuk mengambil data
├── data_preprocessor.py    # Module preprocessing data
├── lstm_model.py           # Implementasi LSTM custom
├── evaluator.py            # Module evaluasi dan visualisasi
├── requirements.txt        # Dependencies
└── README.md              # Dokumentasi
```

## 🔧 Komponen Sistem

### 1. Data Fetcher (`data_fetcher.py`)
- Mengambil data historis dari Yahoo Finance
- Validasi data
- Support untuk 5 cryptocurrency utama

### 2. Data Preprocessor (`data_preprocessor.py`)
- Normalisasi data menggunakan MinMaxScaler
- Windowing untuk sequence data (60 hari → 1 hari)
- Indikator teknikal (SMA, EMA, MACD, RSI)

### 3. LSTM Model (`lstm_model.py`)
- Implementasi LSTM dari scratch menggunakan numpy
- Optimized untuk CPU
- Forward dan backward pass manual

### 4. Evaluator (`evaluator.py`)
- Perhitungan metrics: RMSE, MAE, MAPE
- Akurasi arah pergerakan harga
- Visualisasi grafik prediksi
- Generate trading signals

### 5. Main Predictor (`crypto_predictor.py`)
- Orchestrator untuk semua komponen
- Training loop untuk setiap cryptocurrency
- Generate sinyal trading

## 📈 Output Sistem

### 1. Metrics Evaluasi
- **RMSE**: Root Mean Square Error
- **MAE**: Mean Absolute Error  
- **MAPE**: Mean Absolute Percentage Error
- **Direction Accuracy**: Akurasi arah pergerakan

### 2. Trading Signals
- 🟢 **STRONG BUY**: Prediksi naik >2%
- 🟡 **BUY**: Prediksi naik 0.5-2%
- ⚪ **HOLD**: Perubahan <0.5%
- 🟠 **SELL**: Prediksi turun 0.5-2%
- 🔴 **STRONG SELL**: Prediksi turun >2%

### 3. Visualisasi
- Grafik perbandingan prediksi vs harga aktual
- Disimpan sebagai file PNG untuk setiap cryptocurrency

## ⚡ Optimasi untuk Laptop Spek Rendah

### Memory Optimization
- Batch size kecil (16)
- Hidden layer size dikurangi (32)
- Simplified LSTM implementation

### CPU Optimization  
- Epochs dikurangi (30)
- Efficient numpy operations
- No GPU dependencies

### Performance Tips
- Tutup aplikasi lain saat training
- Gunakan data period yang lebih pendek jika perlu
- Monitor penggunaan RAM

## 📊 Contoh Output

```
🚀 SINYAL TRADING CRYPTOCURRENCY 🚀
============================================================
🟢 BTC    | Saat ini: $43250.1234 | Prediksi: $44100.5678 | Perubahan: +1.97% | Sinyal: BUY
🔴 ETH    | Saat ini: $2650.9876 | Prediksi: $2580.1234 | Perubahan: -2.67% | Sinyal: STRONG SELL
🟡 SOL    | Saat ini: $98.7654 | Prediksi: $99.8765 | Perubahan: +1.12% | Sinyal: BUY
⚪ ADA    | Saat ini: $0.4567 | Prediksi: $0.4578 | Perubahan: +0.24% | Sinyal: HOLD
🟠 XRP    | Saat ini: $0.6234 | Prediksi: $0.6189 | Perubahan: -0.72% | Sinyal: SELL
============================================================
```

## ⚠️ Disclaimer

**PENTING**: Sistem ini dibuat untuk tujuan edukasi dan penelitian. Prediksi harga cryptocurrency sangat berisiko dan tidak boleh dijadikan satu-satunya dasar untuk keputusan investasi.

- Selalu lakukan riset sendiri (DYOR)
- Jangan investasi lebih dari yang mampu Anda tanggung kerugiannya
- Pasar cryptocurrency sangat volatil
- Past performance tidak menjamin future results

## 🔧 Troubleshooting

### Error "Module not found"
```bash
pip install -r requirements.txt
```

### Error "Insufficient data"
- Periksa koneksi internet
- Coba periode data yang lebih panjang
- Beberapa cryptocurrency mungkin tidak tersedia

### Memory Error
- Kurangi batch_size
- Kurangi hidden_size
- Kurangi epochs
- Tutup aplikasi lain

### Slow Performance
- Gunakan konfigurasi default
- Kurangi jumlah epochs
- Pastikan tidak ada aplikasi berat lain yang berjalan

## 📞 Support

Jika mengalami masalah atau memiliki pertanyaan, silakan:
1. Periksa dokumentasi ini
2. Cek error message dengan teliti
3. Pastikan semua dependencies terinstall dengan benar

---

**Happy Trading! 🚀📈**
