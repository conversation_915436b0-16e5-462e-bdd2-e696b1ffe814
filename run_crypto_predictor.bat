@echo off
echo ========================================
echo Cryptocurrency Price Prediction System
echo ========================================
echo Mencari Python di sistem...
echo.

REM Daftar lokasi Python yang mungkin (lokasi instalasi yang umum)
set PYTHON_PATHS[0]="python"
set PYTHON_PATHS[1]="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
set PYTHON_PATHS[2]="C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
set PYTHON_PATHS[3]="C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
set PYTHON_PATHS[4]="C:\Python313\python.exe"
set PYTHON_PATHS[5]="C:\Python312\python.exe"
set PYTHON_PATHS[6]="C:\Python311\python.exe"
set PYTHON_PATHS[7]="C:\Program Files\Python313\python.exe"
set PYTHON_PATHS[8]="C:\Program Files\Python312\python.exe"

REM Coba python dari PATH dulu
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python ditemukan di PATH
    set PYTHON_CMD=python
    goto :found_python
)

REM Coba lokasi AppData Local (instalasi user)
echo Mencoba lokasi AppData Local...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python 3.13 ditemukan di AppData Local
    set PYTHON_CMD="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
    goto :found_python
)

REM Coba lokasi Program Files (instalasi system-wide)
echo Mencoba lokasi Program Files...
"C:\Program Files\Python313\python.exe" --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python 3.13 ditemukan di Program Files
    set PYTHON_CMD="C:\Program Files\Python313\python.exe"
    goto :found_python
)

REM Jika tidak ditemukan
echo ❌ Python tidak ditemukan di lokasi manapun!
echo.
echo 📥 CARA INSTALL PYTHON:
echo 1. Buka browser dan kunjungi: https://www.python.org/downloads/
echo 2. Download Python 3.11 atau 3.12 (lebih stabil dari 3.13)
echo 3. Jalankan installer dan CENTANG "Add Python to PATH"
echo 4. Pilih "Install for all users" jika memungkinkan
echo 5. Setelah install, restart Command Prompt
echo.
echo 🔍 Lokasi yang dicoba:
echo - python (dari PATH)
echo - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\
echo - C:\Program Files\Python313\
echo - C:\Python313\
echo.
echo ⚠️ CATATAN: Folder Start Menu hanya berisi shortcut, bukan executable Python
echo.
pause
exit /b 1

:found_python
echo.
%PYTHON_CMD% --version
echo.

REM Install dependencies
echo 📦 Installing dependencies...
%PYTHON_CMD% -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ⚠️ Error installing dependencies dengan pip biasa
    echo Mencoba dengan --user flag...
    %PYTHON_CMD% -m pip install --user -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Gagal install dependencies
        echo Coba manual: pip install numpy pandas matplotlib yfinance scikit-learn scipy
        pause
        exit /b 1
    )
)

echo ✅ Dependencies berhasil diinstall!
echo.

REM Tanya user mau menjalankan apa
echo 🚀 Pilih mode yang ingin dijalankan:
echo.
echo 1. Test System (Recommended untuk pertama kali)
echo 2. Quick Demo (Demo cepat BTC saja)
echo 3. Full Analysis (Analisis lengkap semua crypto)
echo 4. Exit
echo.
set /p choice="Pilih (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🔍 Menjalankan Test System...
    %PYTHON_CMD% test_system.py
) else if "%choice%"=="2" (
    echo.
    echo ⚡ Menjalankan Quick Demo...
    %PYTHON_CMD% quick_demo.py
) else if "%choice%"=="3" (
    echo.
    echo 🎯 Menjalankan Full Analysis...
    echo ⚠️ Ini akan memakan waktu 15-30 menit
    set /p confirm="Lanjutkan? (y/n): "
    if /i "%confirm%"=="y" (
        %PYTHON_CMD% main.py
    ) else (
        echo Dibatalkan.
    )
) else if "%choice%"=="4" (
    echo Goodbye!
    exit /b 0
) else (
    echo Pilihan tidak valid. Menjalankan Test System...
    %PYTHON_CMD% test_system.py
)

echo.
echo ✅ Selesai!
pause
