"""
Test System
Script untuk testing komponen sistem secara individual
"""

import sys
import traceback

def test_imports():
    """Test semua import yang diperlukan"""
    print("🔍 Testing imports...")
    
    try:
        import numpy as np
        print("✅ numpy imported successfully")
    except ImportError as e:
        print(f"❌ numpy import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except ImportError as e:
        print(f"❌ pandas import failed: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib imported successfully")
    except ImportError as e:
        print(f"❌ matplotlib import failed: {e}")
        return False
    
    try:
        import yfinance as yf
        print("✅ yfinance imported successfully")
    except ImportError as e:
        print(f"❌ yfinance import failed: {e}")
        return False
    
    try:
        from sklearn.preprocessing import MinMaxScaler
        print("✅ scikit-learn imported successfully")
    except ImportError as e:
        print(f"❌ scikit-learn import failed: {e}")
        return False
    
    return True

def test_data_fetcher():
    """Test data fetcher"""
    print("\n🔍 Testing data fetcher...")
    
    try:
        from data_fetcher import CryptoDataFetcher
        
        fetcher = CryptoDataFetcher()
        print("✅ CryptoDataFetcher initialized")
        
        # Test fetch satu cryptocurrency
        print("📊 Testing BTC data fetch...")
        data = fetcher.fetch_crypto_data('BTC', period='5d')  # Ambil data 5 hari saja untuk test
        
        if data is not None and not data.empty:
            print(f"✅ BTC data fetched: {len(data)} records")
            print(f"   Columns: {list(data.columns)}")
            print(f"   Latest close price: ${data['Close'].iloc[-1]:.2f}")
            return True
        else:
            print("❌ Failed to fetch BTC data")
            return False
            
    except Exception as e:
        print(f"❌ Data fetcher test failed: {e}")
        traceback.print_exc()
        return False

def test_preprocessor():
    """Test data preprocessor"""
    print("\n🔍 Testing data preprocessor...")
    
    try:
        from data_preprocessor import CryptoDataPreprocessor
        import numpy as np
        import pandas as pd
        
        # Buat dummy data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        dummy_data = pd.DataFrame({
            'Date': dates,
            'Close': np.random.randn(100).cumsum() + 100
        })
        
        preprocessor = CryptoDataPreprocessor(window_size=10)
        print("✅ CryptoDataPreprocessor initialized")
        
        # Test prepare data
        X_train, y_train, X_test, y_test, scaled_data = preprocessor.prepare_data(dummy_data)
        print(f"✅ Data prepared: Train={len(X_train)}, Test={len(X_test)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Preprocessor test failed: {e}")
        traceback.print_exc()
        return False

def test_lstm_model():
    """Test LSTM model"""
    print("\n🔍 Testing LSTM model...")
    
    try:
        from lstm_model import SimpleLSTM
        import numpy as np
        
        # Buat dummy data
        X_dummy = np.random.randn(10, 20)  # 10 samples, 20 timesteps
        y_dummy = np.random.randn(10, 1)   # 10 targets
        
        model = SimpleLSTM(input_size=1, hidden_size=8, output_size=1)
        print("✅ SimpleLSTM initialized")
        
        # Test forward pass
        predictions = model.predict(X_dummy)
        print(f"✅ Forward pass successful: output shape {predictions.shape}")
        
        # Test training step
        loss = model.train_step(X_dummy, y_dummy)
        print(f"✅ Training step successful: loss = {loss:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ LSTM model test failed: {e}")
        traceback.print_exc()
        return False

def test_evaluator():
    """Test evaluator"""
    print("\n🔍 Testing evaluator...")
    
    try:
        from evaluator import ModelEvaluator
        import numpy as np
        
        evaluator = ModelEvaluator()
        print("✅ ModelEvaluator initialized")
        
        # Test metrics calculation
        y_true = np.array([1, 2, 3, 4, 5])
        y_pred = np.array([1.1, 2.1, 2.9, 4.1, 4.9])
        
        rmse = evaluator.calculate_rmse(y_true, y_pred)
        mae = evaluator.calculate_mae(y_true, y_pred)
        mape = evaluator.calculate_mape(y_true, y_pred)
        
        print(f"✅ Metrics calculated: RMSE={rmse:.4f}, MAE={mae:.4f}, MAPE={mape:.2f}%")
        
        # Test trading signal
        signal = evaluator.generate_trading_signal(100, 105, 'TEST')
        print(f"✅ Trading signal generated: {signal['signal']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluator test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 CRYPTOCURRENCY PREDICTION SYSTEM - TESTING")
    print("=" * 60)
    
    tests = [
        ("Import Dependencies", test_imports),
        ("Data Fetcher", test_data_fetcher),
        ("Data Preprocessor", test_preprocessor),
        ("LSTM Model", test_lstm_model),
        ("Evaluator", test_evaluator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} : {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! System is ready to use.")
        print("Run 'python main.py' to start the full analysis.")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Please check the errors above.")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
