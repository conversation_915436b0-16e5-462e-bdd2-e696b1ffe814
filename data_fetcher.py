"""
Data Fetcher Mo<PERSON>le
Mengambil data historis cryptocurrency dari Yahoo Finance
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class CryptoDataFetcher:
    def __init__(self):
        """
        Inisialisasi data fetcher untuk cryptocurrency
        """
        # Mapping symbol crypto ke Yahoo Finance ticker
        self.crypto_symbols = {
            'BTC': 'BTC-USD',
            'ETH': 'ETH-USD', 
            'SOL': 'SOL-USD',
            'ADA': 'ADA-USD',
            'XRP': 'XRP-USD'
        }
        
    def fetch_crypto_data(self, symbol, period='2y'):
        """
        Mengambil data historis untuk satu cryptocurrency
        
        Args:
            symbol (str): Symbol crypto (BTC, ETH, dll)
            period (str): Periode data ('1y', '2y', '5y', dll)
            
        Returns:
            pandas.DataFrame: Data historis dengan kolom OHLCV
        """
        try:
            # Ambil ticker Yahoo Finance
            ticker = self.crypto_symbols.get(symbol)
            if not ticker:
                raise ValueError(f"Symbol {symbol} tidak didukung")
            
            print(f"Mengambil data {symbol} dari Yahoo Finance...")
            
            # Download data menggunakan yfinance
            crypto = yf.Ticker(ticker)
            data = crypto.history(period=period)
            
            if data.empty:
                raise ValueError(f"Tidak ada data untuk {symbol}")
            
            # Bersihkan data dan reset index
            data = data.dropna()
            data.reset_index(inplace=True)
            
            print(f"Berhasil mengambil {len(data)} data points untuk {symbol}")
            return data
            
        except Exception as e:
            print(f"Error mengambil data {symbol}: {str(e)}")
            return None
    
    def fetch_all_crypto_data(self, period='2y'):
        """
        Mengambil data untuk semua cryptocurrency yang didukung
        
        Args:
            period (str): Periode data
            
        Returns:
            dict: Dictionary dengan key symbol dan value DataFrame
        """
        all_data = {}
        
        for symbol in self.crypto_symbols.keys():
            data = self.fetch_crypto_data(symbol, period)
            if data is not None:
                all_data[symbol] = data
            else:
                print(f"Gagal mengambil data untuk {symbol}")
        
        return all_data
    
    def get_latest_price(self, symbol):
        """
        Mengambil harga terbaru untuk satu cryptocurrency
        
        Args:
            symbol (str): Symbol crypto
            
        Returns:
            float: Harga terbaru
        """
        try:
            ticker = self.crypto_symbols.get(symbol)
            if not ticker:
                return None
                
            crypto = yf.Ticker(ticker)
            data = crypto.history(period='1d')
            
            if not data.empty:
                return data['Close'].iloc[-1]
            return None
            
        except Exception as e:
            print(f"Error mengambil harga terbaru {symbol}: {str(e)}")
            return None
    
    def validate_data(self, data, min_days=100):
        """
        Validasi data yang diambil
        
        Args:
            data (pd.DataFrame): Data yang akan divalidasi
            min_days (int): Minimum jumlah hari data
            
        Returns:
            bool: True jika data valid
        """
        if data is None or data.empty:
            return False
            
        if len(data) < min_days:
            print(f"Data terlalu sedikit: {len(data)} hari (minimum {min_days})")
            return False
            
        # Cek kolom yang diperlukan
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            print(f"Kolom yang hilang: {missing_columns}")
            return False
            
        return True
